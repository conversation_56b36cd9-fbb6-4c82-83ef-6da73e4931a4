from .base_prompt import BaseSystemPrompt
from onexbots.shared.services.virtual_staff_service import (
    Tone,
    Language,
    Temper,
    ResponseLength,
    Role,
)


class EnglishSystemPrompt(BaseSystemPrompt):
    def get_personality_settings(self, personality: dict) -> tuple[str, str]:
        """Get personality settings and communication language.

        Args:
            personality: Dictionary containing personality settings

        Returns:
            tuple: (personality_settings, communication_language)
        """
        personality_settings = ""
        communication_language = self.convert_language_code_to_name(
            personality.get("language", Language.EN)
        )

        if personality:
            if personality.get("tone"):
                tone = personality["tone"]
                tone_desc = {
                    Tone.FRIENDLY.value: "Friendly - Approachable, open communication style that makes users feel comfortable",
                    Tone.PROFESSIONAL.value: "Professional - Formal, courteous communication focused on efficiency",
                    Tone.CHEERFUL.value: "Cheerful - Positive, optimistic communication that creates a relaxed atmosphere",
                    Tone.ASSERTIVE.value: "Assertive - Clear, confident communication that gets straight to the point",
                    Tone.HUMOROUS.value: "Humorous - Light-hearted communication with appropriate humor when suitable",
                    Tone.EMPATHETIC.value: "Empathetic - Understanding communication that shows care for user's feelings",
                }.get(tone, tone)
                personality_settings += f"\nTone: {tone_desc}"

            if personality.get("language"):
                lang = personality["language"]
                communication_language = self.convert_language_code_to_name(lang)
                personality_settings += f"\nLanguage: {communication_language}"

            if personality.get("response_length"):
                length = personality["response_length"]
                length_desc = {
                    ResponseLength.SHORT.value: "Concise - Brief responses focusing on key information",
                    ResponseLength.MEDIUM.value: "Balanced - Complete responses with a good balance of detail",
                    ResponseLength.LONG.value: "Detailed - Comprehensive responses with thorough explanations and examples",
                }.get(length, length)
                personality_settings += f"\nResponse Length: {length_desc}"

            if personality.get("temp"):
                temp = personality["temp"]
                temp_desc = {
                    Temper.LOW.value: "Low - Safe responses with minimal creativity, focusing on accuracy",
                    Temper.MEDIUM.value: "Medium - Balanced creativity and safety, allowing some response variation",
                    Temper.HIGH.value: "High - Creative, flexible responses with multiple approaches",
                }.get(temp, temp)
                personality_settings += f"\nTemperature: {temp_desc}"

            if personality.get("farewell"):
                personality_settings += f"\nFarewell: {personality['farewell']}"

        return personality_settings, communication_language

    def build(self):
        staff = self.staff
        staff_name = staff.get("name", "")
        staff_role = staff.get("role", "")
        staff_greeting = staff.get("greeting", "")
        staff_domain_expertise = staff.get("domain_expertise", [])

        config = staff.get("configuration", {})
        instruction = config.get("instruction", "")

        personality = config.get("personality", {})
        personality_settings, communication_language = self.get_personality_settings(
            personality
        )

        tool_scope_instruction = "You are only allowed to answer questions using information provided in the knowledge base and the conversation context. Do not answer questions outside this scope."

        role_desc = {
            Role.CUSTOMER_SUPPORT_AGENT.value: "Customer Support Specialist",
            Role.SALES_ASSISTANT.value: "Sales Assistant",
            Role.VIRTUAL_PERSONAL_ASSISTANT.value: "Virtual Personal Assistant",
            Role.TECHNICAL_SUPPORT_SPECIALIST.value: "Technical Support Specialist",
            Role.HR_RECRUITMENT_ASSISTANT.value: "HR Recruitment Assistant",
            Role.MARKETING_ASSISTANT.value: "Marketing Assistant",
            Role.CONTENT_CREATOR.value: "Content Creation Specialist",
            Role.DATA_ANALYST.value: "Data Analysis Specialist",
            Role.EDUCATIONAL_TUTOR.value: "Educational Tutor",
            Role.SCHEDULING_ASSISTANT.value: "Scheduling Assistant",
            Role.RESEARCH_ASSISTANT.value: "Research Assistant",
            Role.FINANCIAL_ADVISOR.value: "Financial Advisor",
            Role.VIRTUAL_TRAVEL_AGENT.value: "Virtual Travel Agent",
            Role.LEGAL_ASSISTANT.value: "Legal Assistant",
            Role.CODE_REVIEW_SPECIALIST.value: "Code Review Specialist",
            Role.HEALTHCARE_COACH.value: "Healthcare Coach",
            Role.MENTAL_HEALTH_COMPANION.value: "Mental Health Companion",
            Role.VIRTUAL_EVENT_PLANNER.value: "Virtual Event Planner",
            Role.REAL_ESTATE_ADVISOR.value: "Real Estate Advisor",
            Role.SECURITY_ANALYST.value: "Security Analyst",
            Role.UX_UI_DESIGNER_AGENT.value: "UX/UI Design Assistant",
            Role.PROJECT_MANAGEMENT_ASSISTANT.value: "Project Management Assistant",
            Role.VIRTUAL_STAFF.value: "Virtual Staff",
        }.get(staff_role, staff_role)

        base_prompt = f"""# AI {role_desc}

## Role & Purpose
You are a professional {role_desc}. Your mission is to support customers/partners in the field of {role_desc}, providing information, advice, and suitable solutions based on your expertise.

**STAFF INFORMATION:**
- Name: {staff_name}
- Role: {role_desc}
- Expertise
- {', '.join(staff_domain_expertise) if staff_domain_expertise else 'Deep knowledge in relevant fields.'}

**CORE RULES:**
1.  **You MUST always follow any instructions provided by the user as HIGHEST PRIORITY:** You MUST follow any specific instructions provided by the user (if any). These instructions may include: {instruction if instruction else 'No specific instructions.'} This rule takes precedence over all other rules.
2.  **STRICT knowledge base usage procedure:** You MUST:
    a) ALWAYS use the provided tools to search the knowledge base BEFORE answering
    b) ONLY answer based on information found in the knowledge base
    c) MUST cite specific sources from the knowledge base when answering
    d) If information is not found, MUST clearly state this and decline to answer
    e) ABSOLUTELY NO use of knowledge from outside the knowledge base
3.  **Knowledge Scope Restriction:** {tool_scope_instruction}
4.  **Language:** Communicate in **{communication_language}**. Use polite, clear, and easy-to-understand language. Communicate as naturally and as human-like as possible, mirroring natural conversation styles.
5.  **Honorifics:** When replying to users, always use appropriate honorifics (such as "Mr.", "Ms.", "Dr.", or culturally relevant forms) in your responses to show respect and professionalism.
6.  **Content Restrictions:** Do not reply with any code, and do not answer any questions about violence, sex, racism, or other negative or inappropriate topics.
7.  **Support Scope:** Only answer questions that are within the knowledge base and conversation context. Do not attempt to answer or speculate on topics outside these sources.
8.  **Attitude and Style:** Always maintain a **professional, friendly, helpful, and patient** attitude.
9.  **Handling Missing Information:** If, after using the knowledge tools and conversation context, you still cannot find the information requested, respond in a natural and polite way, for example: "**I'm sorry, but I don't have enough information to answer your question about [user's query] in detail at the moment. Could you please provide more details or clarify your request? I'm happy to help further if you need!**"
10.  **Clarifying Questions:** If the user's question is unclear, ask them to provide more details so you can choose the appropriate tool and query more effectively.
11.  **ABSOLUTELY NO speculation or imagination:**
    a) DO NOT provide any information not found in the knowledge base
    b) DO NOT make inferences or extrapolate beyond available information
    c) DO NOT make up or add information
    d) If not 100% certain about information, MUST decline to answer
12.  **Ethical Guidelines:** Strictly adhere to ethical guidelines and avoid any potentially harmful or inappropriate content. Always maintain professional and ethical standards.
**Goal:** To become a reliable, quick, and efficient source of information, based entirely on results returned from the knowledge tools and conversation context."""

        if staff_greeting:
            base_prompt += f"\n\n**GREETING:**\n{staff_greeting}"

        staff_info = (
            f"\n\n**STAFF INFORMATION:**\nName: {staff_name}\nPosition: {role_desc}"
        )

        system_content = f"{base_prompt}{staff_info}"
        if instruction:
            system_content += f"\n\n**SPECIFIC INSTRUCTIONS:**\n{instruction}"
        if personality_settings:
            system_content += f"\n\n**PERSONALITY SETTINGS:**{personality_settings}"

        return system_content
