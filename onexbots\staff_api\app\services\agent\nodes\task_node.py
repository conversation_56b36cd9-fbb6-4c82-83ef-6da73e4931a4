from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

def task_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle task execution requests.
    
    This node processes requests that involve executing specific tasks,
    actions, or operations. It prepares the state for tool execution
    with task-specific context.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state directing to tool execution
    """
    logger.info("Processing task node - preparing for task tool execution")
    
    messages = state["messages"]
    
    # Extract task-related context from the message
    last_message = messages[-1] if messages else None
    task_context = {}
    
    if last_message and hasattr(last_message, 'content'):
        content = last_message.content.lower()
        
        # Identify task types and priorities
        if any(word in content for word in ["urgent", "asap", "immediately"]):
            task_context["priority"] = "high"
        elif any(word in content for word in ["later", "when possible", "eventually"]):
            task_context["priority"] = "low"
        else:
            task_context["priority"] = "normal"
        
        # Identify task categories
        if any(word in content for word in ["calculate", "compute", "math"]):
            task_context["category"] = "computation"
        elif any(word in content for word in ["search", "find", "lookup"]):
            task_context["category"] = "search"
        elif any(word in content for word in ["create", "generate", "make"]):
            task_context["category"] = "creation"
        elif any(word in content for word in ["analyze", "review", "check"]):
            task_context["category"] = "analysis"
        else:
            task_context["category"] = "general"
        
        # Check for multi-step tasks
        if any(word in content for word in ["then", "after", "next", "step"]):
            task_context["multi_step"] = True
        else:
            task_context["multi_step"] = False
    
    return {
        "messages": messages,
        "next_action": "tool",
        "tool_type": "task",
        "requires_tools": True,
        "task_context": task_context,
        "processing_type": "task_execution"
    }
