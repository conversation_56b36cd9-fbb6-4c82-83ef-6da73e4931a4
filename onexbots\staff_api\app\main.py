from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from prometheus_client import make_asgi_app
from onexbots.shared.config import settings
from datetime import datetime
from fastapi.responses import RedirectResponse

from .api.v1.api import api_router
from .services.mcp.server import MCPServer
from .services.mcp.content_filter import ContentFilterAdapter
from .services.mcp.rate_limiter import RateLimiterAdapter
from dotenv import load_dotenv

load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="ONEXBOTS Staff API",
    description="API for managing staff interactions and AI conversations",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Create health check router
health_router = APIRouter()

@health_router.get("")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "staff-api"
    }

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize MCP server
mcp_server = MCPServer(app)
mcp_server.register_adapter("content_filter", ContentFilterAdapter)
mcp_server.register_adapter("rate_limiter", RateLimiterAdapter)

# Add Prometheus metrics
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Mount health check
app.include_router(health_router, prefix="/health")

# Include API router
app.include_router(api_router, prefix="/api/v1")
