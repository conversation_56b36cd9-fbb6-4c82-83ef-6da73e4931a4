# AI Workflow Implementation

## Overview

The agent builder has been successfully modified to use a custom node-based workflow instead of the built-in `create_react_agent`. This implementation follows the workflow defined in `ai_workflow.mermaid` and provides more granular control over the conversation flow.

## Architecture Changes

### From ReAct Agent to Custom Workflow

**Before:**
- Used LangGraph's `create_react_agent` 
- Simple ReAct (Reasoning + Acting) pattern
- Limited customization options

**After:**
- Custom `StateGraph` with specialized nodes
- Task classification and routing
- Error handling and retry logic
- Platform-specific content formatting

## Node Structure

Each node is implemented as a separate file in the `nodes/` directory:

### Core Workflow Nodes

1. **`classify_task_node.py`** - Entry point that classifies incoming messages
   - Determines if request is: MESSAGE, API, TASK, or HYBRID
   - Uses keyword analysis (can be enhanced with ML models)

2. **`message_node.py`** - Handles simple conversational messages
   - Direct path to LLM response generation
   - No tool execution required

3. **`handle_api_node.py`** - Processes API-related requests
   - Extracts API context (endpoints, operations)
   - Routes to tool execution

4. **`task_node.py`** - Manages task execution requests
   - Analyzes task priority and category
   - Supports multi-step task detection

5. **`hybrid_node.py`** - Handles complex hybrid requests
   - Combines conversational and tool execution
   - Supports sequential/parallel execution strategies

6. **`tool_node.py`** - Executes tools based on context
   - Factory function creates tool node with available tools
   - Handles tool errors and results

7. **`result_node_llm.py`** - Generates final LLM responses
   - Context-aware response generation
   - Incorporates tool results and conversation history

8. **`error_handler_node.py`** - Manages error handling
   - Determines retry vs. format vs. end actions
   - Platform-specific formatting requirements

9. **`format_content_node.py`** - Platform-specific formatting
   - Supports Facebook, Zalo, Telegram, WhatsApp, Web
   - Applies appropriate formatting rules

10. **`retry_node.py`** - Implements retry logic
    - Exponential backoff strategy
    - Maximum retry limits

## Workflow Flow

```
Message → Classify Task → [Message|API|Task|Hybrid] → Tool Node ↔ Tool Node
                                    ↓                      ↓
                              Result Node LLM ← ← ← ← ← ← ←
                                    ↓
                              Error Handler → [Retry|Format|End]
                                    ↓              ↓
                              Format Content → End
```

## State Management

The `State` class extends `MessagesState` with additional fields:

```python
class State(MessagesState):
    context: dict[str, Any]
    task_type: Optional[str] = None
    next_action: Optional[str] = None
    tool_type: Optional[str] = None
    has_error: bool = False
    error_message: Optional[str] = None
    retry_count: int = 0
    format_required: bool = False
    platform: str = "web"
    # ... additional context fields
```

## Key Features

### 1. Task Classification
- Intelligent routing based on message content
- Extensible classification logic
- Support for hybrid requests

### 2. Error Handling & Retry
- Exponential backoff retry strategy
- Error type-specific handling
- Maximum retry limits

### 3. Platform-Specific Formatting
- Facebook Messenger optimization
- Zalo Vietnamese-friendly formatting
- Telegram Markdown support
- WhatsApp character limits
- Web HTML/rich formatting

### 4. Tool Execution
- Context-aware tool selection
- Tool error handling
- Support for tool chaining

### 5. Memory Management
- Preserves existing summarization logic
- Context-aware response generation
- Conversation history management

## Backward Compatibility

The original `build_dynamic_react_agent` function is preserved as a wrapper:

```python
async def build_dynamic_react_agent(staff_id: str) -> CompiledGraph:
    return await build_dynamic_workflow_agent(staff_id)
```

## Usage

```python
# Build the workflow agent
agent = await build_dynamic_workflow_agent(staff_id)

# Execute with messages
result = await agent.ainvoke({
    "messages": [HumanMessage(content="Execute a task for me")],
    "platform": "facebook"  # Optional platform specification
})
```

## Benefits

1. **Granular Control** - Each step of the workflow is customizable
2. **Better Error Handling** - Sophisticated retry and error recovery
3. **Platform Optimization** - Content formatted for specific channels
4. **Extensibility** - Easy to add new node types or modify existing ones
5. **Debugging** - Clear workflow steps for easier troubleshooting
6. **Performance** - Optimized routing reduces unnecessary processing

## Future Enhancements

1. **ML-Based Classification** - Replace keyword-based classification with ML models
2. **Dynamic Tool Selection** - AI-powered tool selection based on context
3. **Advanced Retry Strategies** - Circuit breaker patterns, adaptive backoff
4. **Workflow Analytics** - Performance monitoring and optimization
5. **A/B Testing** - Compare different workflow configurations
