name: 🚀 Server Deployment

on:
    push:
        tags:
            - "v*.*.*"
            - "v*.*.*-*"
    workflow_dispatch:

jobs:
    deploy:
        runs-on: self-hosted
        timeout-minutes: 60

        steps:
            - name: 📥 Checkout code
              uses: actions/checkout@v3

            - name: 🔖 Get Git tag
              id: tag
              run: echo "TAG=${GITHUB_REF_NAME}" >> $GITHUB_OUTPUT

            - name: 🔧 Debug
              run: echo "Tag is ${{ steps.tag.outputs.TAG }}"

            - name: ⚙️ Set environment
              run: |
                  echo "GITHUB_REF_NAME=${GITHUB_REF_NAME}" >> $GITHUB_ENV
                  if [[ "${GITHUB_REF_NAME}" == "main" ]]; then
                  echo "DEPLOY_ENV=Production" >> $GITHUB_ENV
                  echo "ENV_FILE=.env.prod" >> $GITHUB_ENV
                  echo "COMPOSE_FILE=docker-compose.yml:docker-compose.prod.yml" >> $GITHUB_ENV
                  echo "DEPLOY_URL=https://agent.onexbots.com" >> $GITHUB_ENV
                  else
                  echo "DEPLOY_ENV=Development" >> $GITHUB_ENV
                  echo "ENV_FILE=.env.dev" >> $GITHUB_ENV
                  echo "COMPOSE_FILE=docker-compose.yml:docker-compose.override.yml" >> $GITHUB_ENV
                  echo "DEPLOY_URL=https://dev-agent.onexbots.com" >> $GITHUB_ENV
                  fi

            - name: 🔐 Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v2
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-southeast-1

            - name: 🔄 Copy .env phù hợp
              run: |
                  cp /home/<USER>/actions-runner/onexbots/.env $GITHUB_WORKSPACE/onexbots/.env

            - name: 🐳 Build & Push Docker Images to AWS ECR
              working-directory: ./onexbots
              env:
                  AWS_REGION: ap-southeast-1
                  ECR_REPOSITORY_1: staff_api
                  ECR_REPOSITORY_2: rag_pipeline
              run: |
                  set -e

                  TAG=${{ steps.tag.outputs.TAG }}
                  echo "🔧 Using tag: $TAG"

                  # 🔐 Login vào AWS ECR
                  aws ecr get-login-password --region $AWS_REGION | \
                    docker login --username AWS \
                    --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com

                  # === 🔨 Build & Push: staff_api ===
                  echo "🚀 Building $ECR_REPOSITORY_1..."
                  docker build -t $ECR_REPOSITORY_1:$TAG -f $ECR_REPOSITORY_1/Dockerfile .
                  docker tag $ECR_REPOSITORY_1:$TAG ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_1:$TAG
                  docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_1:$TAG

                  # === 🔨 Build & Push: rag_pipeline ===
                  echo "🚀 Building $ECR_REPOSITORY_2..."
                  docker build -t $ECR_REPOSITORY_2:$TAG -f $ECR_REPOSITORY_2/Dockerfile .
                  docker tag $ECR_REPOSITORY_2:$TAG ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_2:$TAG
                  docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_2:$TAG

                  echo "✅ Docker images pushed successfully!"

            - name: 🚀 Deploy to Server bots
              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.HOST }}
                  username: ${{ secrets.USERNAME }}
                  password: ${{ secrets.PASSWORD }}
                  port: 22
                  envs: AWS_ACCESS_KEY_ID,AWS_SECRET_ACCESS_KEY,AWS_REGION,AWS_ACCOUNT_ID,TAG
                  script: |
                      set -e
                      
                      echo "🔐 AWS ECR login..."
                      aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"
                      
                      cd onexbots_services/onexbots-images
                      
                      ECR_REGISTRY="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"
                      
                      STAFF_API_IMAGE="$ECR_REGISTRY/staff_api:$TAG"
                      RAG_PIPELINE_IMAGE="$ECR_REGISTRY/rag_pipeline:$TAG"
                      
                      echo "🔁 Update .env..."
                      grep -q '^STAFF_API_IMAGE=' .env && sed -i "s|^STAFF_API_IMAGE=.*|STAFF_API_IMAGE=$STAFF_API_IMAGE|" .env || echo "STAFF_API_IMAGE=$STAFF_API_IMAGE" >> .env
                      grep -q '^RAG_PIPELINE_IMAGE=' .env && sed -i "s|^RAG_PIPELINE_IMAGE=.*|RAG_PIPELINE_IMAGE=$RAG_PIPELINE_IMAGE|" .env || echo "RAG_PIPELINE_IMAGE=$RAG_PIPELINE_IMAGE" >> .env
                      
                      echo "📥 Pull images..."
                      docker compose pull
                      
                      echo "🔄 Restarting services..."
                      docker compose down
                      docker compose up -d
              env:
                  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  AWS_REGION: ${{ secrets.AWS_REGION }}
                  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
                  TAG: ${{ steps.tag.outputs.TAG }}


            - name: 📢 Send Success Notification
              if: success()
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  DEPLOY_ENV: ${{ env.DEPLOY_ENV }}
                  GITHUB_REF_NAME: ${{ github.ref_name }}
                  DEPLOY_URL: ${{ env.DEPLOY_URL }}
                  SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
                  GITHUB_ACTOR: ${{ github.actor }}
                  PR_BODY: ${{ github.event.pull_request.body }}
                  PR_AUTHOR: ${{ github.event.pull_request.user.login }}
                  PR_TITLE: ${{ github.event.pull_request.title }}
                  PR_NUMBER: ${{ github.event.pull_request.number }}
                  PR_URL: ${{ github.event.pull_request.html_url }}
                  JIRA_LINK: https://onexbots.atlassian.net/browse/
                  COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
                  COMMIT_URL: ${{ github.event.head_commit.url }}
              run: |
                  if [ -n "$SLACK_DEV_USER_IDS" ]; then
                    MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/.*/<@&>/' | paste -sd' ' -)
                  else
                    MENTIONS=""
                  fi

                  # fallback author nếu không có pull_request
                  AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}

                  FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")

                  if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
                    PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
                  else
                    COMMIT_SHORT_SHA=$(echo "$COMMIT_URL" | grep -o '[a-f0-9]\{7\}$')
                    PR_TEXT="$COMMIT_MESSAGE"
                  fi

                  # Escape double quotes and backslashes in PR_TEXT for JSON
                  PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$/\\n/' | tr -d '\n')

                  curl -X POST https://slack.com/api/chat.postMessage \
                  -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                  -H "Content-type: application/json" \
                  --data "$(cat <<EOF
                  {
                    "channel": "#prd-onexbots-deployment",
                    "text": "✅ Deployment Successful on $DEPLOY_ENV (\`$GITHUB_REF_NAME\`)",
                    "blocks": [
                      {
                        "type": "header",
                        "text": { "type": "plain_text", "text": "✅ Deployment Successful!", "emoji": true }
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Author:*\n$AUTHOR" },
                          { "type": "mrkdwn", "text": "*Deployment URL:*\n<$DEPLOY_URL|View Deployment>" }
                        ]
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Details:*\n• $PR_BLOCK" }
                        ]
                      },
                      {
                        "type": "context",
                        "elements": [
                          { "type": "mrkdwn", "text": "👀 *Please review the code changes and provide feedback!*\ncc: $MENTIONS" }
                        ]
                      }
                    ]
                  }
                  EOF
                  )"

            - name: 📢 Send Failure Notification
              if: failure()
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  DEPLOY_ENV: ${{ env.DEPLOY_ENV }}
                  GITHUB_REF_NAME: ${{ github.ref_name }}
                  GITHUB_SERVER_URL: ${{ github.server_url }}
                  GITHUB_REPOSITORY: ${{ github.repository }}
                  GITHUB_RUN_ID: ${{ github.run_id }}
                  SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
                  GITHUB_ACTOR: ${{ github.actor }}
                  PR_BODY: ${{ github.event.pull_request.body }}
                  PR_AUTHOR: ${{ github.event.pull_request.user.login }}
                  PR_TITLE: ${{ github.event.pull_request.title }}
                  PR_NUMBER: ${{ github.event.pull_request.number }}
                  PR_URL: ${{ github.event.pull_request.html_url }}
                  JIRA_LINK: https://onexbots.atlassian.net/browse/
                  COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
                  COMMIT_URL: ${{ github.event.head_commit.url }}
              run: |
                  if [ -n "$SLACK_DEV_USER_IDS" ]; then
                    MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/.*/<@&>/' | paste -sd' ' -)
                  else
                    MENTIONS=""
                  fi

                  # Fallback cho Author
                  AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}

                  FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")

                  if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
                    PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
                  else
                    COMMIT_SHORT_SHA=$(echo "$COMMIT_URL" | grep -o '[a-f0-9]\{7\}$')
                    PR_TEXT="*Commit:*\n<$COMMIT_URL|$COMMIT_SHORT_SHA> - $COMMIT_MESSAGE"
                  fi

                  # Escape double quotes and backslashes in PR_TEXT for JSON
                  PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$/\\n/' | tr -d '\n')

                  curl -X POST https://slack.com/api/chat.postMessage \
                  -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                  -H "Content-type: application/json" \
                  --data "$(cat <<EOF
                  {
                    "channel": "#prd-onexbots-deployment",
                    "text": "❌ Deployment Failed on $DEPLOY_ENV (\`$GITHUB_REF_NAME\`)",
                    "blocks": [
                      {
                        "type": "header",
                        "text": { "type": "plain_text", "text": "❌ Deployment Failed!", "emoji": true }
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Author:*\n$AUTHOR" },
                          { "type": "mrkdwn", "text": "*Branch:*\n\`$GITHUB_REF_NAME\`" }
                        ]
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Details:*\n• $PR_BLOCK" }
                        ]
                      },
                      {
                        "type": "section",
                        "text": {
                          "type": "mrkdwn",
                          "text": "*Workflow Logs:*\n<$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID|View Logs>"
                        }
                      },
                      {
                        "type": "context",
                        "elements": [
                          { "type": "mrkdwn", "text": "⚠️ *Please check the logs and fix the issue.*\ncc: $MENTIONS" }
                        ]
                      }
                    ]
                  }
                  EOF
                  )"
