flowchart TD
    A[Message] --> B[Classify task Node]
    
    B -->|Message| C[Message Node]
    B -->|API| D[Handle API Node]
    B -->|Task| E[Task Node]
    B -->|Hybrid| F[Hybrid Node]

   
    D-->O[Tool Node]
    O-.->D
    E-->O[Tool Node]
    O-.->E
    F-->O[Tool Node]
    O-.->F
   
    C --> G[Result Node LLM]
    D --> G
    E --> G
    B --> G
    F --> G
    
    J -.->|Yes| B
    
    
    G --> L[Error Handler Node]
    L --> M[Format Content Node]
    L --> J[Retry Node]
    J -->|No| N[End]
    M -.->|Web| N
    M -.->|Facebook| N
    M -.->|Zalo| N