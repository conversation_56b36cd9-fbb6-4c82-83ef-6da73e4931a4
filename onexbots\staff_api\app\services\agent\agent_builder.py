from typing import Dict, List, Literal, Optional, Any

from langgraph.graph import (
    MessagesState,
    StateGraph,
    START,
    END,
)
from langgraph.graph.state import (
    CompiledGraph,
)
from langgraph.prebuilt import ToolNode
from onexbots.shared.services.virtual_staff_service import get_virtual_staff

import logging
from langchain_openai import ChatOpenAI

from async_lru import alru_cache
from onexbots.staff_api.app.services.prompts.registry import get_system_prompt
from onexbots.shared.config import settings
from .tools.get_tools import get_knowledge_tools, get_tools_for_staff
# Import the nodes
from .nodes import (
    classify_task_node,
    message_node,
    handle_api_node,
    task_node,
    hybrid_node,
    create_tool_node,
    create_result_node_llm,
    error_handler_node,
    format_content_node,
    retry_node,
    TASK_TYPES,
)

# Configure logger
logger = logging.getLogger(__name__)


class State(MessagesState):
    context: dict[str, Any]
    remaining_steps: int = 5
    task_type: Optional[str] = None
    next_action: Optional[str] = None
    tool_type: Optional[str] = None
    has_error: bool = False
    error_message: Optional[str] = None
    retry_count: int = 0
    format_required: bool = False
    platform: str = "web"
    max_retries_reached: bool = False
    should_retry: bool = False
    processing_type: Optional[str] = None
    api_context: Optional[Dict[str, Any]] = None
    task_context: Optional[Dict[str, Any]] = None
    hybrid_context: Optional[Dict[str, Any]] = None


# @alru_cache(maxsize=256, ttl=900)
async def build_dynamic_workflow_agent(staff_id: str) -> CompiledGraph:
    staff_data = get_virtual_staff(staff_id)
    staff = staff_data.get("data")
    logger.info("Build dynamic agent function called")
    knowledge_tools = await get_knowledge_tools(staff)
    skill_tools = get_tools_for_staff(staff)
    all_tools = skill_tools + knowledge_tools

    system_prompt = get_system_prompt(staff)

    # Get model name from configuration
    model_name = (
        staff["configuration"]
        .get("llm_settings", {})
        .get("llm_model", {})
        .get("model_name", "gpt-4o-mini")
    )
    model = ChatOpenAI(model=model_name)

    # Create workflow nodes
    tool_node = create_tool_node(all_tools)
    result_node = create_result_node_llm(model, system_prompt)

    # Define routing functions
    def route_after_classify(state: State):
        """Route after task classification."""
        task_type = state.get("task_type", TASK_TYPES["MESSAGE"])
        if task_type == TASK_TYPES["MESSAGE"]:
            return "message_node"
        elif task_type == TASK_TYPES["API"]:
            return "handle_api_node"
        elif task_type == TASK_TYPES["TASK"]:
            return "task_node"
        elif task_type == TASK_TYPES["HYBRID"]:
            return "hybrid_node"
        else:
            return "message_node"  # Default fallback

    def route_to_tools_or_result(state: State):
        """Route to tools or result based on next_action."""
        next_action = state.get("next_action", "result")
        if next_action == "tool":
            return "tool_node"
        else:
            return "result_node"

    def route_after_error_handler(state: State):
        """Route after error handling."""
        should_retry = state.get("should_retry", False)
        format_required = state.get("format_required", False)
        max_retries_reached = state.get("max_retries_reached", False)

        if should_retry and not max_retries_reached:
            return "retry_node"
        elif format_required:
            return "format_content_node"
        else:
            return END

    def route_after_retry(state: State):
        """Route after retry logic."""
        max_retries_reached = state.get("max_retries_reached", False)
        if max_retries_reached:
            return END
        else:
            return "classify_task_node"  # Start over

    # Build the workflow graph
    workflow = StateGraph(State)

    # Add nodes
    workflow.add_node("classify_task_node", classify_task_node)
    workflow.add_node("message_node", message_node)
    workflow.add_node("handle_api_node", handle_api_node)
    workflow.add_node("task_node", task_node)
    workflow.add_node("hybrid_node", hybrid_node)
    workflow.add_node("tool_node", tool_node)
    workflow.add_node("result_node", result_node)
    workflow.add_node("error_handler_node", error_handler_node)
    workflow.add_node("format_content_node", format_content_node)
    workflow.add_node("retry_node", retry_node)

    # Set entry point
    workflow.set_entry_point("classify_task_node")

    # Add conditional edges from classify_task_node
    workflow.add_conditional_edges(
        "classify_task_node",
        route_after_classify,
        {
            "message_node": "message_node",
            "handle_api_node": "handle_api_node",
            "task_node": "task_node",
            "hybrid_node": "hybrid_node",
        },
    )

    # Add edges from processing nodes to tools or result
    for node in ["message_node", "handle_api_node", "task_node", "hybrid_node"]:
        workflow.add_conditional_edges(
            node,
            route_to_tools_or_result,
            {"tool_node": "tool_node", "result_node": "result_node"},
        )

    # Tool node can loop back to itself or go to result
    workflow.add_conditional_edges(
        "tool_node",
        route_to_tools_or_result,
        {"tool_node": "tool_node", "result_node": "result_node"},
    )

    # Result node goes to error handler
    workflow.add_edge("result_node", "error_handler_node")

    # Error handler routes to retry, format, or end
    workflow.add_conditional_edges(
        "error_handler_node",
        route_after_error_handler,
        {
            "retry_node": "retry_node",
            "format_content_node": "format_content_node",
            END: END,
        },
    )

    # Retry node routes back to classify or end
    workflow.add_conditional_edges(
        "retry_node",
        route_after_retry,
        {"classify_task_node": "classify_task_node", END: END},
    )

    # Format content node ends the workflow
    workflow.add_edge("format_content_node", END)

    # Compile and return the workflow
    agent = workflow.compile()
    return agent


# Backward compatibility alias
async def build_dynamic_react_agent(staff_id: str) -> CompiledGraph:
    """
    Backward compatibility function that calls the new workflow-based agent builder.

    Args:
        staff_id: The ID of the virtual staff

    Returns:
        Compiled workflow graph
    """
    logger.info("Using backward compatibility wrapper for build_dynamic_react_agent")
    return await build_dynamic_workflow_agent(staff_id)
