from typing import Dict, List, Literal, Optional, Any

from langchain_core.tools import BaseTool
from langgraph.graph import (
    MessagesState,
)
from langgraph.graph.state import (
    CompiledGraph,
)
from langgraph.prebuilt import create_react_agent
from onexbots.shared.services.virtual_staff_service import get_virtual_staff
from onexbots.shared.services.virtual_staff_service import VirtualStaff
from onexbots.shared.services.knowledge_service import KnowledgeService
from onexbots.shared.services.embedding_service import EmbeddingService
from ..tools.registry import ToolManager
import logging
from langchain.tools.retriever import create_retriever_tool
from langchain_openai import ChatOpenAI
from langmem.short_term import SummarizationNode
from langchain_core.messages.utils import count_tokens_approximately
from async_lru import alru_cache
from onexbots.staff_api.app.services.prompts.registry import get_system_prompt
from langchain_core.prompts import ChatPromptTemplate
from onexbots.shared.config import settings
from langchain_core.messages import HumanMessage

# Configure logger
logger = logging.getLogger(__name__)

# Add this at the top-level, after logger definition
DEFAULT_TOOLS = ["timer"]

# Custom summary prompts to retain user-provided facts
INITIAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "Create a summary of the conversation above. IMPORTANT: Always retain any facts the user provides about themselves (e.g., name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

EXISTING_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "This is the summary so far: {existing_summary}\n\nExtend this summary by taking into account the new messages above. IMPORTANT: Do not lose any user-provided facts (such as name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

FINAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{system_message}"),
        ("system", "Summary of the conversation so far: {summary}"),
        ("placeholder", "{messages}"),
    ]
)


def should_continue(state: MessagesState) -> Literal["tools", "__end__"]:
    """Determine whether to continue calling tools or end the process."""
    messages = state["messages"]
    if not messages:
        return "__end__"  # End if no messages (edge case)
    last_message = messages[-1]
    # If the LLM called a tool, continue to the tools node
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "tools"
    # Otherwise, end the cycle
    return "__end__"


def get_tools_for_staff(staff: VirtualStaff) -> List[BaseTool]:
    """Get tools based on staff configuration."""
    tools_list: List[BaseTool] = []
    tool_manager = ToolManager()
    # Get enabled tools from staff configuration
    enabled_tools = staff["configuration"].get("tools", {}).get("enabled", [])
    if enabled_tools is None:
        enabled_tools = []
    tool_configs = staff["configuration"].get("tools", {}).get("config", {})
    # Always include default tools
    enabled_tools = list(set(enabled_tools + DEFAULT_TOOLS))
    if not enabled_tools:
        return []
    for tool_name in enabled_tools:
        try:
            # Get tool configuration
            tool_config = None
            if tool_configs:
                tool_config = tool_configs.get(tool_name, {})
            # Get tool instance
            tool = tool_manager.get_tool(tool_name)
            if tool:
                # Configure tool if needed
                if tool_config:
                    # Update tool configuration
                    for key, value in tool_config.items():
                        if hasattr(tool, key):
                            setattr(tool, key, value)

                tools_list.append(tool)
                logger.debug(f"Added tool: {tool_name}")
            else:
                logger.warning(f"Tool not found: {tool_name}")

        except Exception as e:
            logger.error(f"Error loading tool {tool_name}: {str(e)}")

    logger.info(f"Loaded {len(tools_list)} tools for staff {staff['name']}")
    return tools_list


async def test_add_or_update_knowledge(knowledge_id: str):
    """Test adding or updating knowledge document."""
    embedding_service = EmbeddingService(settings)
    knowledge_service = KnowledgeService()
    knowledge = knowledge_service.get(knowledge_id)
    content = """
Các yêu cầu từ Doanh nghiệp len sợi & OneXBots

100%
E10:E12

- Thông tin công ty:
- Công ty TNHH NoLi Handmade - NOLI HANDMADE COMPANY LIMITED
- Hotline: **********
- Email: <EMAIL>

- Các kênh online của NoLi:
🌸Website: https://shop.noli.vn
🌼Shopee NoLi HN: https://shopee.vn/nolihandmadehn
🌼Shopee NoLi HCM: https://shopee.vn/nolihandmadehochiminh
❤️Lazada: https://lzd.co/nolihandmade
⭐️Tiki: https://tiki.vn/cua-hang/noli-handmade
🌳Tiktok NoLi: https://www.tiktok.com/@nolihandmade

- Mua trực tiếp:
NoLi Handmade HN
🏡 Số 41 ngõ 63 Phố Ô Đồng Lầm, đường ven Hồ Ba Mẫu, Phương Liên, Đống Đa, Hà Nội
⏰ Mở cửa: từ 8h-21h. Từ T2 đến Chủ nhật
☎️ Hotline: ********** (zalo)
🧶 Chỉ đường tới cửa hàng NoLi: https://maps.app.goo.gl/HmtRz7XXnzVCeP6KA

- Mua trực tiếp:
NoLi Handmade HCM
🏡Số M4-21 đường M4, khu Manhattan Glory, Vinhomes Grandpark, Long Bình, Thủ Đức
⏰Mở cửa: 9h-17h. Từ T2-T7
☎️ Hotline: ********** (zalo)
🧶 Chỉ đường tới: https://maps.app.goo.gl/aKPmoNWzfXsZ8ddN7

- STK:
Chủ tài khoản: Mai Khánh Linh
*MBBank: **********
* Momo: **********

❤️ Học đan móc online dễ dàng cùng NoLi:
Website: https://noli.vn
Youtube: https://www.youtube.com/@NoLiHandmadeChannel
Group: https://www.facebook.com/groups/lopmocbalolennhung

- Danh sách sản phẩm và mô tả sản phẩm: https://docs.google.com/spreadsheets/d/1JR4UOXeCUG7I2DVscu-YOtgLGEc9qpSl/edit?usp=sharing&ouid=114889868491849224087&rtpof=true&sd=true
- Video hướng dẫn: https://youtu.be/zjVD1kbNo2M?si=Qn7lzj4g9FhjjECW
- Thông tin công ty:
- Công ty TNHH NoLi Handmade - NOLI HANDMADE COMPANY LIMITED
- Hotline: **********
- Email: <EMAIL>

- Các kênh online của NoLi:
🌸Website: https://shop.noli.vn
🌼Shopee NoLi HN: https://shopee.vn/nolihandmadehn
🌼Shopee NoLi HCM: https://shopee.vn/nolihandmadehochiminh
❤️Lazada: https://lzd.co/nolihandmade
⭐️Tiki: https://tiki.vn/cua-hang/noli-handmade
🌳Tiktok NoLi: https://www.tiktok.com/@nolihandmade

- Mua trực tiếp:
NoLi Handmade HN
🏡 Số 41 ngõ 63 Phố Ô Đồng Lầm, đường ven Hồ Ba Mẫu, Phương Liên, Đống Đa, Hà Nội
⏰ Mở cửa: từ 8h-21h. Từ T2 đến Chủ nhật
☎️ Hotline: ********** (zalo)
🧶 Chỉ đường tới cửa hàng NoLi: https://maps.app.goo.gl/HmtRz7XXnzVCeP6KA

- Mua trực tiếp:
NoLi Handmade HCM
🏡Số M4-21 đường M4, khu Manhattan Glory, Vinhomes Grandpark, Long Bình, Thủ Đức
⏰Mở cửa: 9h-17h. Từ T2-T7
☎️ Hotline: ********** (zalo)
🧶 Chỉ đường tới: https://maps.app.goo.gl/aKPmoNWzfXsZ8ddN7

- STK:
Chủ tài khoản: Mai Khánh Linh
*MBBank: **********
* Momo: **********

❤️ Học đan móc online dễ dàng cùng NoLi:
Website: https://noli.vn
Youtube: https://www.youtube.com/@NoLiHandmadeChannel
Group: https://www.facebook.com/groups/lopmocbalolennhung

- Danh sách sản phẩm và mô tả sản phẩm: https://docs.google.com/spreadsheets/d/1JR4UOXeCUG7I2DVscu-YOtgLGEc9qpSl/edit?usp=sharing&ouid=114889868491849224087&rtpof=true&sd=true
- Video hướng dẫn: https://youtu.be/zjVD1kbNo2M?si=Qn7lzj4g9FhjjECW
Bật chế độ hỗ trợ trình đọc màn hình
Để bật chế độ hỗ trợ đọc màn hình, nhấn Ctrl+Alt+Z Để tìm hiểu thêm về các phím tắt, nhấn Ctrl+dấu gạch chéo
"""
    metadata = {"type": "text", "knowledge_id": knowledge_id, "source": "DIRECT_TEXT"}

    try:
        # Test adding new knowledge document
        logger.info("Testing add new knowledge document...")
        embedding_knowledge = await embedding_service.add_or_update_knowledge_document(
            knowledge_id=knowledge_id,
            content=content,
            metadata=metadata,
            source="DIRECT_TEXT",
        )
        return embedding_knowledge
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        raise


async def get_knowledge_tools(staff: VirtualStaff) -> List[BaseTool]:
    """Get knowledge tools based on staff configuration."""
    knowledge_tools: List[BaseTool] = []
    logger.info(staff.get("configuration").get("knowledge_base").get("knowledge_list"))
    # Check if knowledge base is enabled
    if not staff["configuration"].get("knowledge_base", {}).get("enabled"):
        logger.info(f"Knowledge base disabled for staff {staff['name']}")
        return knowledge_tools
    # Initialize knowledge service
    knowledge_service = KnowledgeService()
    # Get knowledge IDs from configuration
    knowledge_ids = (
        staff["configuration"].get("knowledge_base", {}).get("knowledge_ids", [])
    )
    if not knowledge_ids:
        return []

    for knowledge_id in knowledge_ids:
        # Get retriever for this knowledge
        # await test_add_or_update_knowledge(knowledge_id)
        retriever = await knowledge_service.retriever(knowledge_id)
        if retriever:
            try:
                # Get the first document to extract summary using ainvoke
                docs = await retriever.ainvoke("")
                direction = docs[0].metadata.get("direction") if docs else None

                # Use summary from retriever if available, otherwise use a default description
                description = direction or f"Knowledge base for {knowledge_id}"

                knowledge_tool = create_retriever_tool(
                    name=f"knowledge_{knowledge_id.replace('-', '_')}",
                    retriever=retriever,
                    description=description,
                )
                knowledge_tools.append(knowledge_tool)
                logger.debug(f"Added knowledge tool for ID: {knowledge_id}")
            except Exception as e:
                logger.error(
                    f"Error creating knowledge tool for ID {knowledge_id}: {str(e)}"
                )
                continue
        else:
            logger.warning(
                f"Could not create retriever for knowledge ID: {knowledge_id}"
            )

    logger.info(
        f"Loaded {len(knowledge_tools)} knowledge tools for staff {staff['name']}"
    )
    return knowledge_tools


class State(MessagesState):
    context: dict[str, Any]
    remaining_steps: int = 5


# @alru_cache(maxsize=256, ttl=900)
async def build_dynamic_react_agent(staff_id: str) -> CompiledGraph:
    staff_data = get_virtual_staff(staff_id)
    staff = staff_data.get("data")
    logger.info("Build dynamic agent function called")
    knowledge_tools = await get_knowledge_tools(staff)
    skill_tools = get_tools_for_staff(staff)
    all_tools = skill_tools + knowledge_tools

    system_prompt = get_system_prompt(staff)

    # Get model name from configuration
    model_name = (
        staff["configuration"]
        .get("llm_settings", {})
        .get("llm_model", {})
        .get("model_name", "gpt-4o-mini")
    )

    model = ChatOpenAI(model="gpt-4o-mini")

    summarization_node = SummarizationNode(
        token_counter=count_tokens_approximately,
        model=model,
        max_tokens=8000,
        max_summary_tokens=700,
        # max_tokens=1000,
        # max_summary_tokens=700,
        output_messages_key="llm_input_messages",
        initial_summary_prompt=INITIAL_SUMMARY_PROMPT,
        existing_summary_prompt=EXISTING_SUMMARY_PROMPT,
        final_prompt=FINAL_SUMMARY_PROMPT,
    )

    def custom_pre_model_hook(state):
        messages = state["messages"]
        context = state.get("context", {})
        if not messages:
            return state
        last_human_idx = None

        # get last human message
        for i in range(len(messages) - 1, -1, -1):
            if isinstance(messages[i], HumanMessage):
                last_human_idx = i
                break

        if last_human_idx is None:
            summary = summarization_node.invoke(state)
            context = summary.get("context")
            return {**state, "context": context}

        # Only include context in summary_state if it is not None and not empty
        if context:
            summary_state = {"messages": messages[:last_human_idx], "context": context}
        else:
            summary_state = {"messages": messages[:last_human_idx]}
        summary = summarization_node.invoke(summary_state)
        context = summary.get("context")
        remaining_messages = messages[last_human_idx:]
        new_messages = summary.get("llm_input_messages", []) + remaining_messages
        # Only include context if it is not None and not empty
        new_state = {**state, "llm_input_messages": new_messages}
        if context:
            new_state["context"] = context
        elif "context" in new_state:
            del new_state["context"]
        return new_state

    # Create and return the ReAct agent with summarization node
    agent = create_react_agent(
        model=model_name,
        tools=all_tools,
        prompt=system_prompt,
        state_schema=State,
        pre_model_hook=custom_pre_model_hook,
    )
    return agent
