from typing import Dict, Any, List
from langgraph.graph import MessagesState
from langchain_core.tools import BaseTool
from langchain_core.messages import ToolMessage
import logging

logger = logging.getLogger(__name__)

def create_tool_node(tools: List[BaseTool]):
    """
    Create a tool execution node with the provided tools.
    
    This factory function creates a tool node that can execute
    the appropriate tools based on the current state and context.
    
    Args:
        tools: List of available tools for execution
        
    Returns:
        Tool node function
    """
    
    def tool_node(state: MessagesState) -> Dict[str, Any]:
        """
        Execute tools based on the current state and tool type.
        
        This node handles tool execution for API calls, tasks, and hybrid operations.
        It filters and executes the appropriate tools based on the context.
        
        Args:
            state: Current conversation state
            
        Returns:
            Dict with updated state including tool results
        """
        logger.info("Executing tool node")
        
        messages = state["messages"]
        tool_type = state.get("tool_type", "general")
        
        # Get the last message to check for tool calls
        if not messages:
            logger.warning("No messages found in tool node")
            return {"messages": messages, "tool_results": []}
        
        last_message = messages[-1]
        tool_results = []
        
        # Check if the last message has tool calls
        if hasattr(last_message, "tool_calls") and last_message.tool_calls:
            logger.info(f"Executing {len(last_message.tool_calls)} tool calls")
            
            for tool_call in last_message.tool_calls:
                tool_name = tool_call["name"]
                tool_args = tool_call["args"]
                tool_id = tool_call["id"]
                
                # Find the appropriate tool
                selected_tool = None
                for tool in tools:
                    if tool.name == tool_name:
                        selected_tool = tool
                        break
                
                if selected_tool:
                    try:
                        logger.debug(f"Executing tool: {tool_name} with args: {tool_args}")
                        
                        # Execute the tool
                        result = selected_tool.invoke(tool_args)
                        
                        # Create tool message
                        tool_message = ToolMessage(
                            content=str(result),
                            tool_call_id=tool_id,
                            name=tool_name
                        )
                        
                        tool_results.append(tool_message)
                        logger.info(f"Tool {tool_name} executed successfully")
                        
                    except Exception as e:
                        logger.error(f"Error executing tool {tool_name}: {str(e)}")
                        
                        # Create error tool message
                        error_message = ToolMessage(
                            content=f"Error executing {tool_name}: {str(e)}",
                            tool_call_id=tool_id,
                            name=tool_name
                        )
                        tool_results.append(error_message)
                else:
                    logger.warning(f"Tool {tool_name} not found in available tools")
                    
                    # Create not found tool message
                    not_found_message = ToolMessage(
                        content=f"Tool {tool_name} not available",
                        tool_call_id=tool_id,
                        name=tool_name
                    )
                    tool_results.append(not_found_message)
        
        # Add tool results to messages
        updated_messages = messages + tool_results
        
        # Determine next action based on tool type and results
        next_action = "result"  # Default to result generation
        
        # For hybrid operations, might need additional processing
        if tool_type == "hybrid":
            hybrid_context = state.get("hybrid_context", {})
            if hybrid_context.get("execution_strategy") == "sequential":
                # Check if more tools need to be executed
                if len(tool_results) < len(hybrid_context.get("execution_order", [])):
                    next_action = "tool"  # Continue with more tools
        
        return {
            "messages": updated_messages,
            "tool_results": tool_results,
            "next_action": next_action,
            "tools_executed": len(tool_results)
        }
    
    return tool_node
