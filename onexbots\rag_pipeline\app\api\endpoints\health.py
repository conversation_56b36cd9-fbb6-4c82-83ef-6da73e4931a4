from fastapi import APIRouter

router = APIRouter()

@router.get("/health")
async def health_check():
    """
    Check the health status of the service.
    
    Returns:
        dict: Health status information
    """
    return {
        "status": "healthy",
    }

@router.get("/ready")
async def readiness_check():
    """
    Check if the service is ready to handle requests.
    
    Returns:
        dict: Readiness status information
    """
    return {
        "status": "ready",
    }