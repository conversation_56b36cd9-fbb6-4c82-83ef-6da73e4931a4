name: ⏰ Notify PRs waiting too long (test - 5 mins)

on:
  schedule:
    - cron: '*/5 * * * *'  # chạy mỗi 5 phút
  workflow_dispatch:

jobs:
  notify:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Get PRs opened ≥ 5 minutes
        id: waiting_prs
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const result = await github.paginate(github.rest.pulls.list, {
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: "open",
              per_page: 100,
            });

            const now = new Date();

            const waiting = result.map(pr => {
              const createdAt = new Date(pr.created_at);
              const diffMinutes = Math.floor((now - createdAt) / 1000 / 60);

              return {
                title: pr.title,
                number: pr.number,
                url: pr.html_url,
                user: pr.user.login,
                created_at: createdAt.toISOString(),
                minutes_waiting: diffMinutes
              };
            }).filter(pr =>
              pr.minutes_waiting >= 5
            );

            core.setOutput("prs", JSON.stringify(waiting));

      - name: 📣 Notify Slack
        if: ${{ steps.waiting_prs.outputs.prs != '' && fromJson(steps.waiting_prs.outputs.prs).length > 0 }}
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          PRS_JSON: ${{ steps.waiting_prs.outputs.prs }}
        run: |
          echo "$PRS_JSON" | jq -c '.[]' | while read -r pr; do
            title=$(echo "$pr" | jq -r '.title')
            url=$(echo "$pr" | jq -r '.url')
            number=$(echo "$pr" | jq -r '.number')
            user=$(echo "$pr" | jq -r '.user')
            minutes_waiting=$(echo "$pr" | jq -r '.minutes_waiting')

            curl -X POST https://slack.com/api/chat.postMessage \
              -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
              -H "Content-type: application/json" \
              --data "$(cat <<EOF
          {
            "channel": "#review-pull-requests",
            "text": ":alarm_clock: PR #$number đã chờ $minutes_waiting phút",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "⏱️ Pull Request Chờ Review",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*PR #:*\n#$number"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Tác giả:*\n@$user"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Thời gian chờ:*\n$minutes_waiting phút"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Tiêu đề:* $title\n🔗 <$url|Xem Pull Request>"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "⚠️ Vui lòng review sớm!\ncc: @channel"
                  }
                ]
              }
            ]
          }
          EOF
              )"
          done
