from typing import Dict, Any
from langgraph.graph import MessagesState
import logging

logger = logging.getLogger(__name__)

def message_node(state: MessagesState) -> Dict[str, Any]:
    """
    Handle simple message responses without tools.
    
    This node processes conversational messages that don't require
    tool execution or API calls. It prepares the state for direct
    LLM response generation.
    
    Args:
        state: Current conversation state
        
    Returns:
        Dict with updated state directing to result generation
    """
    logger.info("Processing message node - simple conversation")
    
    messages = state["messages"]
    
    # For simple messages, we go directly to result generation
    return {
        "messages": messages, 
        "next_action": "result",
        "requires_tools": False,
        "processing_type": "conversational"
    }
